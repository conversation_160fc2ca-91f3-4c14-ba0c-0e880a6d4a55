import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { CartesianChart, Line, Bar, Area } from 'victory-native';
import { Colors } from '../../constants/Colors';
import { globalStyles } from '../../constants/globalStyles';
import spacingScale from '../../constants/spacing';

const { width } = Dimensions.get('window');
const chartWidth = width - (spacingScale.lg * 2);

// Sample data for demonstration
const sampleData = [
  { x: 1, y: 4.2 },
  { x: 2, y: 3.8 },
  { x: 3, y: 4.5 },
  { x: 4, y: 4.1 },
  { x: 5, y: 4.7 },
  { x: 6, y: 4.3 },
  { x: 7, y: 4.6 },
];

const barData = [
  { x: 'Kahve', y: 4.2 },
  { x: 'Atmosfer', y: 4.5 },
  { x: 'Servis', y: 4.1 },
  { x: 'Fiyat', y: 3.8 },
];

export default function VictoryChartExample() {
  return (
    <View style={styles.container}>
      <Text style={[styles.title, globalStyles.subheading]}>
        Victory Native Kurulumu Başarılı! ✅
      </Text>

      <Text style={[styles.description, globalStyles.body]}>
        Victory Native başarıyla yüklendi ve kullanıma hazır.
        Grafik bileşenleri artık projenizde kullanılabilir.
      </Text>

      <View style={styles.featureList}>
        <Text style={[styles.featureTitle, globalStyles.bodyBold]}>
          Mevcut Özellikler:
        </Text>
        <Text style={[styles.feature, globalStyles.body]}>
          • CartesianChart - Koordinat sistemi grafikleri
        </Text>
        <Text style={[styles.feature, globalStyles.body]}>
          • Line - Çizgi grafikleri
        </Text>
        <Text style={[styles.feature, globalStyles.body]}>
          • Bar - Çubuk grafikleri
        </Text>
        <Text style={[styles.feature, globalStyles.body]}>
          • Area - Alan grafikleri
        </Text>
        <Text style={[styles.feature, globalStyles.body]}>
          • Skia tabanlı yüksek performans
        </Text>
        <Text style={[styles.feature, globalStyles.body]}>
          • Reanimated ile smooth animasyonlar
        </Text>
      </View>

      <View style={styles.infoBox}>
        <Text style={[styles.infoTitle, globalStyles.bodyBold]}>
          Kullanım İçin:
        </Text>
        <Text style={[styles.infoText, globalStyles.bodySmall]}>
          import {`{CartesianChart, Line, Bar, Area}`} from 'victory-native';
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: spacingScale.lg,
    backgroundColor: Colors.background,
  },
  title: {
    textAlign: 'center',
    marginBottom: spacingScale.lg,
    color: Colors.text,
  },
  description: {
    textAlign: 'center',
    marginBottom: spacingScale.xl,
    color: Colors.textSecondary,
    lineHeight: 24,
  },
  featureList: {
    backgroundColor: Colors.surface || Colors.card,
    borderRadius: 12,
    padding: spacingScale.lg,
    marginBottom: spacingScale.lg,
  },
  featureTitle: {
    color: Colors.text,
    marginBottom: spacingScale.md,
  },
  feature: {
    color: Colors.textSecondary,
    marginBottom: spacingScale.sm,
    paddingLeft: spacingScale.sm,
  },
  infoBox: {
    backgroundColor: Colors.primary + '10',
    borderRadius: 8,
    padding: spacingScale.md,
    borderLeftWidth: 4,
    borderLeftColor: Colors.primary,
  },
  infoTitle: {
    color: Colors.primary,
    marginBottom: spacingScale.sm,
  },
  infoText: {
    color: Colors.textSecondary,
    fontFamily: 'monospace',
  },
});
