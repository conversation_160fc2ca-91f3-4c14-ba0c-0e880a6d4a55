<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="26113b1b-f4b8-4d91-b62f-b7f2a7aec26c" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/App.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/assets/adaptive-icon.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/assets/favicon.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/assets/icon.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/assets/splash-icon.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/index.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/package-lock.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/package.json" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="device_and_snapshot_combo_box_target[]" />
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <task path="">
          <activation />
        </task>
        <projects_view />
      </state>
    </system>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2yKFzGns0onKE0vSHo00g7y3rBq" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Gradle.Unnamed.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.readMode.enableVisualFormatting&quot;: &quot;true&quot;,
    &quot;cf.first.check.clang-format&quot;: &quot;false&quot;,
    &quot;cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;com.google.services.firebase.aqiPopupShown&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/siptracker-clean&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="Unnamed" type="GradleRunConfiguration" factoryName="Gradle" nameIsGenerated="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" value="" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list />
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>false</RunAsTest>
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="26113b1b-f4b8-4d91-b62f-b7f2a7aec26c" name="Changes" comment="" />
      <created>1749575619758</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749575619758</updated>
    </task>
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>