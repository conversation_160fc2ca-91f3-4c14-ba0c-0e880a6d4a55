import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Modal, 
  ScrollView,
  Dimensions 
} from 'react-native';
import Slider from '@react-native-community/slider';
import { Ionicons } from '@expo/vector-icons';
import Animated, { 
  FadeInDown, 
  FadeInUp,
  useSharedValue,
  useAnimatedStyle,
  withSpring
} from 'react-native-reanimated';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';
import spacingScale from '../constants/spacing';

const { width } = Dimensions.get('window');

export default function ExploreWizard({ visible, onClose, onApplyFilters }) {
  const [coffeeWeight, setCoffeeWeight] = useState(3);
  const [vibeWeight, setVibeWeight] = useState(3);
  const [serviceWeight, setServiceWeight] = useState(3);

  const scale = useSharedValue(0);

  React.useEffect(() => {
    if (visible) {
      scale.value = withSpring(1, { damping: 15, stiffness: 150 });
    } else {
      scale.value = withSpring(0, { damping: 15, stiffness: 150 });
    }
  }, [visible]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handleApply = () => {
    const weights = {
      coffee: coffeeWeight,
      vibe: vibeWeight,
      service: serviceWeight
    };
    onApplyFilters(weights);
    onClose();
  };

  const handleReset = () => {
    setCoffeeWeight(3);
    setVibeWeight(3);
    setServiceWeight(3);
  };

  const getWeightLabel = (weight) => {
    if (weight === 0) return 'Hiç Önemli Değil';
    if (weight === 1) return 'Az Önemli';
    if (weight === 2) return 'Biraz Önemli';
    if (weight === 3) return 'Orta';
    if (weight === 4) return 'Önemli';
    if (weight === 5) return 'Çok Önemli';
    return 'Orta';
  };

  const WeightSlider = ({ title, icon, value, onValueChange, color }) => {
    const scaleValue = useSharedValue(1);

    const animatedThumbStyle = useAnimatedStyle(() => ({
      transform: [{ scale: scaleValue.value }],
    }));

    const handleValueChange = (newValue) => {
      // Haptic feedback on value change
      scaleValue.value = withSpring(1.2, { duration: 100 }, () => {
        scaleValue.value = withSpring(1, { duration: 200 });
      });
      onValueChange(newValue);
    };

    return (
      <Animated.View
        entering={FadeInDown.delay(200)}
        style={styles.sliderContainer}
      >
        <View style={styles.sliderHeader}>
          <Text style={styles.sliderIcon}>{icon}</Text>
          <Text style={[styles.sliderTitle, globalStyles.body]}>{title}</Text>
          <View style={[styles.valueChip, { backgroundColor: color }]}>
            <Text style={styles.valueChipText}>{value}</Text>
          </View>
        </View>

        <View style={styles.sliderRow}>
          <Slider
            style={styles.slider}
            minimumValue={0}
            maximumValue={5}
            step={1}
            value={value}
            onValueChange={handleValueChange}
            minimumTrackTintColor={color}
            maximumTrackTintColor={Colors.border || '#E0E0E0'}
            thumbStyle={{
              backgroundColor: color,
              width: 24,
              height: 24,
              borderRadius: 12,
              elevation: 3,
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.25,
              shadowRadius: 3.84,
            }}
          />
        </View>

        <Text style={[styles.weightLabel, globalStyles.bodySmall]}>
          {getWeightLabel(value)}
        </Text>
      </Animated.View>
    );
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <Animated.View style={[styles.container, animatedStyle]}>
          <ScrollView 
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scrollContent}
          >
            {/* Header */}
            <Animated.View 
              entering={FadeInUp.delay(100)}
              style={styles.header}
            >
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Ionicons name="close" size={24} color={Colors.text} />
              </TouchableOpacity>
              <Text style={[styles.title, globalStyles.heading]}>
                🔮 Keşfet Sihirbazı
              </Text>
              <Text style={[styles.subtitle, globalStyles.body]}>
                Bugün sana en uygun mekanları bulmak için önceliklerini belirle
              </Text>
            </Animated.View>

            {/* Sliders */}
            <View style={styles.slidersContainer}>
              <WeightSlider
                title="Kahve Lezzeti"
                icon="☕"
                value={coffeeWeight}
                onValueChange={setCoffeeWeight}
                color="#8B4513"
              />
              
              <WeightSlider
                title="Mekan Atmosferi"
                icon="🏠"
                value={vibeWeight}
                onValueChange={setVibeWeight}
                color="#FF6B6B"
              />
              
              <WeightSlider
                title="Servis Kalitesi"
                icon="👥"
                value={serviceWeight}
                onValueChange={setServiceWeight}
                color="#4ECDC4"
              />
            </View>

            {/* Action Buttons */}
            <Animated.View 
              entering={FadeInUp.delay(400)}
              style={styles.buttonContainer}
            >
              <TouchableOpacity 
                style={styles.resetButton} 
                onPress={handleReset}
              >
                <Ionicons name="refresh" size={20} color={Colors.tabIconDefault} />
                <Text style={[styles.resetText, globalStyles.bodySmall]}>
                  Sıfırla
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.applyButton} 
                onPress={handleApply}
              >
                <Ionicons name="search" size={20} color="white" />
                <Text style={[styles.applyText, globalStyles.body]}>
                  Bana Uygun Mekanları Göster
                </Text>
              </TouchableOpacity>
            </Animated.View>
          </ScrollView>
        </Animated.View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacingScale.lg,
  },
  container: {
    backgroundColor: Colors.card,
    borderRadius: 20,
    width: width * 0.9,
    maxHeight: '85%',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
  },
  scrollContent: {
    padding: spacingScale.xl,
  },
  header: {
    alignItems: 'center',
    marginBottom: spacingScale.xl,
  },
  closeButton: {
    position: 'absolute',
    top: 0,
    right: 0,
    padding: spacingScale.sm,
  },
  title: {
    color: Colors.text,
    textAlign: 'center',
    marginBottom: spacingScale.sm,
    fontSize: 24,
    fontWeight: 'bold',
  },
  subtitle: {
    color: Colors.tabIconDefault,
    textAlign: 'center',
    lineHeight: 22,
  },
  slidersContainer: {
    marginBottom: spacingScale.xl,
  },
  sliderContainer: {
    marginBottom: spacingScale.xl,
    backgroundColor: Colors.background,
    borderRadius: 12,
    padding: spacingScale.lg,
  },
  sliderHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacingScale.md,
  },
  sliderIcon: {
    fontSize: 24,
    marginRight: spacingScale.sm,
  },
  sliderTitle: {
    color: Colors.text,
    fontWeight: '600',
    flex: 1,
  },
  valueChip: {
    minWidth: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  valueChipText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  sliderRow: {
    marginBottom: spacingScale.sm,
  },
  slider: {
    width: '100%',
    height: 50,
  },
  weightLabel: {
    color: Colors.tabIconDefault,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  resetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacingScale.md,
    borderRadius: 8,
    backgroundColor: Colors.background,
  },
  resetText: {
    color: Colors.tabIconDefault,
    marginLeft: spacingScale.xs,
  },
  applyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary,
    paddingHorizontal: spacingScale.lg,
    paddingVertical: spacingScale.md,
    borderRadius: 25,
    flex: 1,
    marginLeft: spacingScale.md,
    justifyContent: 'center',
  },
  applyText: {
    color: 'white',
    fontWeight: '600',
    marginLeft: spacingScale.sm,
  },
});
